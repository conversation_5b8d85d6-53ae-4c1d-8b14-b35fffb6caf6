# SoulMemoir Installer Script
# This script downloads and installs SoulMemoir automatically
# Bypasses Windows SmartScreen warnings

Write-Host "==================================" -ForegroundColor Cyan
Write-Host "    SoulMemoir Installer v1.0.0   " -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "Requesting administrator privileges..." -ForegroundColor Yellow
    Start-Process PowerShell -Verb RunAs -ArgumentList "-ExecutionPolicy Bypass -File `"$PSCommandPath`""
    exit
}

Write-Host "Starting SoulMemoir installation..." -ForegroundColor Green
Write-Host ""

# Create temp directory
$tempDir = "$env:TEMP\SoulMemoir"
if (!(Test-Path $tempDir)) {
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
}

# Download installer
$installerUrl = "https://soulmemoir.com/downloads/SoulMemoir_Installer_v1.0.0.exe"
$installerPath = "$tempDir\SoulMemoir_Installer_v1.0.0.exe"

Write-Host "Downloading SoulMemoir installer..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath -UseBasicParsing
    Write-Host "Download completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Error downloading installer: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Verify file exists
if (!(Test-Path $installerPath)) {
    Write-Host "Error: Installer file not found!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Running SoulMemoir installer..." -ForegroundColor Green
Write-Host "Please follow the installation wizard." -ForegroundColor Yellow
Write-Host ""

# Run installer with bypass
try {
    Start-Process -FilePath $installerPath -Wait -Verb RunAs
    Write-Host ""
    Write-Host "Installation completed!" -ForegroundColor Green
    Write-Host "You can now launch SoulMemoir from your desktop or start menu." -ForegroundColor Cyan
} catch {
    Write-Host "Error running installer: $($_.Exception.Message)" -ForegroundColor Red
}

# Cleanup
Write-Host ""
Write-Host "Cleaning up temporary files..." -ForegroundColor Yellow
Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "Thank you for installing SoulMemoir!" -ForegroundColor Cyan
Write-Host "Visit https://soulmemoir.com for support and updates." -ForegroundColor Gray
Write-Host ""
Read-Host "Press Enter to exit"

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoulMemoir - Coming Soon</title>
    <link rel="icon" type="image/png" href="soulmemoir_brand_logo.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            overflow: hidden;
        }

        .coming-soon-container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo img {
            width: 50px;
            height: 50px;
            border-radius: 10px;
        }

        .brand-name {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ffffff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .tagline {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .coming-soon-text {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .description {
            font-size: 1.1rem;
            margin-bottom: 3rem;
            opacity: 0.8;
            line-height: 1.6;
        }

        .notify-form {
            display: flex;
            gap: 1rem;
            max-width: 400px;
            margin: 0 auto 2rem;
        }

        .notify-input {
            flex: 1;
            padding: 1rem;
            border: none;
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .notify-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .notify-btn {
            padding: 1rem 2rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .notify-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .social-link {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .social-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }

        /* Animated background elements */
        .bg-animation {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(10deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .brand-name {
                font-size: 2.5rem;
            }
            
            .coming-soon-text {
                font-size: 1.5rem;
            }
            
            .notify-form {
                flex-direction: column;
            }
            
            .notify-btn {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="bg-animation">
        <div class="floating-element">
            <i class="fas fa-heart" style="font-size: 3rem;"></i>
        </div>
        <div class="floating-element">
            <i class="fas fa-star" style="font-size: 2.5rem;"></i>
        </div>
        <div class="floating-element">
            <i class="fas fa-sparkles" style="font-size: 2rem;"></i>
        </div>
    </div>

    <div class="coming-soon-container">
        <div class="logo">
            <img src="soulmemoir_brand_logo.png" alt="SoulMemoir Logo">
        </div>
        
        <h1 class="brand-name">SoulMemoir</h1>
        <p class="tagline">Transform Your Inner Thoughts Into Meaningful Stories</p>
        
        <h2 class="coming-soon-text">Coming Soon</h2>
        <p class="description">
            We're putting the finishing touches on something beautiful. 
            SoulMemoir will help you capture and reflect on your life's most meaningful moments.
        </p>
        
        <form class="notify-form" onsubmit="handleNotify(event)">
            <input type="email" class="notify-input" placeholder="Enter your email for updates" required>
            <button type="submit" class="notify-btn">
                <i class="fas fa-bell"></i> Notify Me
            </button>
        </form>
        
        <div class="social-links">
            <a href="#" class="social-link" aria-label="Twitter">
                <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="social-link" aria-label="Facebook">
                <i class="fab fa-facebook"></i>
            </a>
            <a href="#" class="social-link" aria-label="Instagram">
                <i class="fab fa-instagram"></i>
            </a>
            <a href="#" class="social-link" aria-label="LinkedIn">
                <i class="fab fa-linkedin"></i>
            </a>
        </div>
    </div>

    <script>
        function handleNotify(event) {
            event.preventDefault();
            const email = event.target.querySelector('.notify-input').value;
            
            // Simple email validation
            if (email) {
                alert(`Thank you! We'll notify you at ${email} when SoulMemoir launches.`);
                event.target.reset();
            }
        }
    </script>
</body>
</html>

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --secondary-color: #8b5cf6;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --background: #ffffff;
    --surface: #f9fafb;
    --border: #e5e7eb;
    --shadow: rgba(0, 0, 0, 0.1);
    --shadow-lg: rgba(0, 0, 0, 0.15);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', Georgia, serif;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: white;
    color: var(--text-primary);
    border: 1px solid var(--border);
    box-shadow: 0 2px 8px var(--shadow);
}

.btn-secondary:hover {
    background: var(--surface);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--shadow-lg);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    transition: all 0.3s ease;
}

.nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.1);
}

.brand-name {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -0.25rem;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    gap: 1rem;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-primary);
    cursor: pointer;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #fef3c7 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    inset: 0;
    pointer-events: none;
}

/* Unique Memory Bubbles */
.memory-bubble {
    position: absolute;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s ease;
    animation: bubbleFloat 8s ease-in-out infinite;
}

.memory-bubble:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.memory-bubble:nth-child(2) { animation-delay: -2s; }
.memory-bubble:nth-child(3) { animation-delay: -4s; }
.memory-bubble:nth-child(4) { animation-delay: -6s; }

.bubble-content {
    text-align: center;
    color: white;
}

.bubble-content i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.bubble-text {
    font-size: 0.875rem;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.memory-bubble:hover .bubble-text {
    opacity: 1;
}

/* Unique Constellation Effect */
.constellation {
    position: absolute;
    inset: 0;
    pointer-events: none;
}

.constellation-line {
    position: absolute;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
    animation: constellationPulse 4s ease-in-out infinite;
}

/* Quote Particles */
.quote-particle {
    position: absolute;
    font-size: 6rem;
    color: rgba(59, 130, 246, 0.1);
    font-family: 'Playfair Display', serif;
    animation: quoteFloat 10s ease-in-out infinite;
    pointer-events: none;
}

.quote-particle:nth-child(2) { animation-delay: -3s; }
.quote-particle:nth-child(3) { animation-delay: -6s; }

@keyframes bubbleFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-15px) rotate(2deg); }
    50% { transform: translateY(-10px) rotate(-1deg); }
    75% { transform: translateY(-20px) rotate(1deg); }
}

@keyframes constellationPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

@keyframes quoteFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.1; }
    50% { transform: translateY(-30px) rotate(5deg); opacity: 0.3; }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.hero-content {
    text-align: center;
    max-width: 800px;
    z-index: 10;
    position: relative;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 2rem;
    color: var(--primary-color);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 2rem;
    animation: slideUp 0.8s ease-out;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    animation: slideUp 0.8s ease-out 0.2s both;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2.5rem;
    line-height: 1.7;
    animation: slideUp 0.8s ease-out 0.4s both;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
    animation: slideUp 0.8s ease-out 0.6s both;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto;
    animation: slideUp 0.8s ease-out 0.8s both;
}

.stat {
    text-align: center;
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
    color: var(--primary-color);
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.stat:hover .stat-icon {
    background: rgba(59, 130, 246, 0.2);
    transform: scale(1.1);
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    animation: slideUp 1s ease-out 1s both;
}

.scroll-mouse {
    width: 1.5rem;
    height: 2.5rem;
    border: 2px solid var(--text-muted);
    border-radius: 1rem;
    position: relative;
    display: flex;
    justify-content: center;
}

.scroll-wheel {
    width: 0.25rem;
    height: 0.75rem;
    background: var(--text-muted);
    border-radius: 0.125rem;
    margin-top: 0.5rem;
    animation: scroll 2s ease-in-out infinite;
}

@keyframes scroll {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(0.75rem);
        opacity: 0;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Section Styles */
.section-header {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 4rem;
}

.section-header h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    line-height: 1.7;
}

/* Features Section */
.features {
    padding: 6rem 0;
    background: var(--surface);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2.5rem;
    border-radius: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(229, 231, 235, 0.5);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.feature-icon.heart {
    background: linear-gradient(135deg, #fef3c7, #fbbf24);
    color: #d97706;
}

.feature-icon.book {
    background: linear-gradient(135deg, #dbeafe, #3b82f6);
    color: #1d4ed8;
}

.feature-icon.target {
    background: linear-gradient(135deg, #f3e8ff, #8b5cf6);
    color: #7c3aed;
}

.feature-icon.lightning {
    background: linear-gradient(135deg, #ecfdf5, #10b981);
    color: #059669;
}

.feature-icon.shield {
    background: linear-gradient(135deg, #fef2f2, #ef4444);
    color: #dc2626;
}

.feature-icon.file {
    background: linear-gradient(135deg, #f0f9ff, #06b6d4);
    color: #0891b2;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.feature-card h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Chapters Section */
.chapters {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #fef3c7 100%);
}

.chapters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.chapter-card {
    background: white;
    padding: 2rem;
    border-radius: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left: 4px solid;
    position: relative;
    overflow: hidden;
}

.chapter-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    opacity: 0.1;
}

.chapter-card.childhood {
    border-left-color: #fbbf24;
}

.chapter-card.childhood::before {
    background: #fbbf24;
}

.chapter-card.love {
    border-left-color: #ef4444;
}

.chapter-card.love::before {
    background: #ef4444;
}

.chapter-card.growth {
    border-left-color: #10b981;
}

.chapter-card.growth::before {
    background: #10b981;
}

.chapter-card.achievements {
    border-left-color: #8b5cf6;
}

.chapter-card.achievements::before {
    background: #8b5cf6;
}

.chapter-card.legacy {
    border-left-color: #3b82f6;
}

.chapter-card.legacy::before {
    background: #3b82f6;
}

.chapter-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.chapter-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    color: white;
}

.childhood .chapter-icon {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.love .chapter-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.growth .chapter-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.achievements .chapter-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.legacy .chapter-icon {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.chapter-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.chapter-card > p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.chapter-reflections {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.reflection {
    background: var(--surface);
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-style: italic;
    border-left: 3px solid rgba(59, 130, 246, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links,
    .nav-actions {
        display: none;
    }

    .mobile-menu-btn {
        display: block;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .container {
        padding: 0 1rem;
    }

    .features-grid,
    .chapters-grid {
        grid-template-columns: 1fr;
    }

    .feature-card,
    .chapter-card {
        padding: 2rem 1.5rem;
    }
}

/* Life Reflections Section */
.reflections {
    padding: 6rem 0;
    background: white;
}

.reflections-showcase {
    max-width: 800px;
    margin: 0 auto 4rem;
    position: relative;
}

.reflection-carousel {
    position: relative;
    height: 300px;
    overflow: hidden;
    border-radius: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%);
}

.reflection-slide {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 3rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.5s ease;
}

.reflection-slide.active {
    opacity: 1;
    transform: translateX(0);
}

.reflection-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: white;
}

.reflection-icon.childhood {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.reflection-icon.growth {
    background: linear-gradient(135deg, #10b981, #059669);
}

.reflection-icon.love {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.reflection-icon.legacy {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.reflection-slide h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-family: 'Playfair Display', serif;
}

.reflection-slide p {
    color: var(--text-secondary);
    font-size: 1.125rem;
}

.reflection-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.reflection-btn {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    border: 2px solid var(--border);
    background: white;
    color: var(--text-secondary);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reflection-btn.active,
.reflection-btn:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.reflection-navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 1rem;
    pointer-events: none;
}

.nav-btn {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: all;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-btn:hover {
    background: white;
    transform: scale(1.1);
}

.prompts-grid h3 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.5rem;
}

.mini-reflections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.mini-reflection {
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    text-align: center;
    font-weight: 500;
    color: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.mini-reflection:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.mini-reflection.childhood {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.mini-reflection.love {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.mini-reflection.growth {
    background: linear-gradient(135deg, #10b981, #059669);
}

.mini-reflection.achievements {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.mini-reflection.legacy {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

/* Unique Life Timeline */
.life-timeline {
    padding: 6rem 0;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.life-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>');
    opacity: 0.3;
}

.life-timeline .section-header {
    position: relative;
    z-index: 2;
}

.life-timeline .section-header h2,
.life-timeline .section-header p {
    color: white;
}

.timeline-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.timeline-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #3b82f6, #8b5cf6, #ef4444, #f59e0b);
    transform: translateX(-50%);
    border-radius: 2px;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.timeline-item {
    position: relative;
    margin-bottom: 4rem;
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-marker {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    position: relative;
    z-index: 3;
    margin: 0 2rem;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
    border: 4px solid rgba(255, 255, 255, 0.2);
}

.timeline-marker.childhood {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.timeline-marker.growth {
    background: linear-gradient(135deg, #10b981, #059669);
}

.timeline-marker.achievements {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.timeline-marker.legacy {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.timeline-content {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.timeline-content:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.timeline-content h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: white;
}

.timeline-content p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.timeline-reflections {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.reflection-tag {
    background: rgba(59, 130, 246, 0.2);
    color: rgba(255, 255, 255, 0.9);
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-style: italic;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.timeline-cta {
    text-align: center;
    margin-top: 4rem;
    position: relative;
    z-index: 2;
}

.timeline-message {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    font-style: italic;
}

/* Unique Soul Insights Section */
.soul-insights {
    padding: 6rem 0;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 50%, #f59e0b 100%);
    position: relative;
    overflow: hidden;
}

.soul-insights::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
}

.insights-quiz {
    max-width: 800px;
    margin: 0 auto 4rem;
    position: relative;
    z-index: 2;
}

.quiz-step {
    display: none !important;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 3rem;
    border-radius: 1.5rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.5);
    text-align: center;
}

.quiz-step.active {
    display: block !important;
    animation: fadeInUp 0.5s ease-out;
}

.quiz-progress {
    margin-bottom: 2rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(59, 130, 246, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
    transition: width 0.5s ease;
}

.progress-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.quiz-form {
    max-width: 400px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 1.5rem;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border);
    border-radius: 0.75rem;
    background: white;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input::placeholder {
    color: var(--text-muted);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.quiz-next {
    margin-top: 1rem;
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.quiz-results {
    text-align: center;
}

.results-header {
    margin-bottom: 3rem;
}

.results-header i {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.results-header h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.user-greeting {
    font-size: 1.25rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.results-content {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;
}

.result-style, .result-recommendations {
    background: var(--surface);
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid var(--border);
}

.result-style h4, .result-recommendations h4 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.style-result {
    font-size: 1.125rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.recommendation-item {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 3px solid var(--primary-color);
    text-align: left;
    font-size: 0.95rem;
    color: var(--text-secondary);
}

.result-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.quiz-question {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 3rem;
    border-radius: 1.5rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.quiz-question h3 {
    text-align: center;
    font-size: 1.75rem;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.quiz-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.quiz-option {
    background: white;
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: 1rem;
    padding: 1.5rem;
    cursor: not-allowed !important;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: left;
    opacity: 0.6 !important;
    pointer-events: none !important;
}

.quiz-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.quiz-option i {
    font-size: 2rem;
    color: var(--primary-color);
    min-width: 3rem;
}

.quiz-option span {
    font-weight: 500;
    color: var(--text-primary);
}

.reflection-styles {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
    position: relative;
    z-index: 2;
}

.style-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2.5rem;
    border-radius: 1.5rem;
    text-align: center;
    transition: all 0.4s ease;
    border: 1px solid rgba(255, 255, 255, 0.5);
    position: relative;
    overflow: hidden;
}

.style-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.style-card.analytical::before {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.style-card.creative::before {
    background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.style-card.intuitive::before {
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.style-card.structured::before {
    background: linear-gradient(90deg, #10b981, #059669);
}

.style-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.style-card:hover::before {
    opacity: 1;
}

.style-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    margin: 0 auto 1.5rem;
    color: white;
    transition: all 0.3s ease;
}

.analytical .style-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.creative .style-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.intuitive .style-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.structured .style-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.style-card:hover .style-icon {
    transform: scale(1.1) rotate(5deg);
}

.style-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.style-card p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.style-features {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.feature {
    background: var(--surface);
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.insights-cta {
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 3rem;
    border-radius: 1.5rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
}

.cta-content h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.cta-content p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Testimonials Section */
.testimonials {
    padding: 6rem 0;
    background: var(--surface);
}

.testimonials-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto 4rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-family: 'Playfair Display', serif;
}

.stat-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: white;
    padding: 2.5rem;
    border-radius: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.testimonial-rating {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1.5rem;
    color: #fbbf24;
}

.testimonial-card blockquote {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--text-primary);
    margin-bottom: 2rem;
    font-style: italic;
    position: relative;
}

.testimonial-card blockquote::before {
    content: '"';
    font-size: 4rem;
    color: var(--primary-color);
    position: absolute;
    top: -1rem;
    left: -1rem;
    opacity: 0.3;
    font-family: 'Playfair Display', serif;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.author-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.author-role {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Download Section */
.download {
    padding: 6rem 0;
    background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
    color: white;
}

.download-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.download-text h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: 1.5rem;
    color: white;
}

.download-text p {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.download-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: rgba(255, 255, 255, 0.9);
}

.feature-item i {
    color: #10b981;
    font-size: 1.125rem;
}

.download-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.download-links {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.download-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.download-link:hover {
    color: white;
}

.download-link.upcoming {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.download-link.upcoming span {
    color: rgba(255, 255, 255, 0.5);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
    background: rgba(255, 255, 255, 0.1) !important;
    color: rgba(255, 255, 255, 0.5) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
}

.download-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.app-mockup {
    position: relative;
    transform: perspective(1000px) rotateY(-15deg) rotateX(10deg);
    transition: transform 0.3s ease;
}

.app-mockup:hover {
    transform: perspective(1000px) rotateY(-10deg) rotateX(5deg);
}

.mockup-screen {
    width: 300px;
    height: 400px;
    background: white;
    border-radius: 1.5rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    border: 8px solid #1f2937;
}

.mockup-header {
    background: #f9fafb;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mockup-dots {
    display: flex;
    gap: 0.5rem;
}

.mockup-dots span {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background: #d1d5db;
}

.mockup-dots span:first-child {
    background: #ef4444;
}

.mockup-dots span:nth-child(2) {
    background: #fbbf24;
}

.mockup-dots span:last-child {
    background: #10b981;
}

.mockup-title {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.875rem;
}

.mockup-content {
    padding: 2rem 1.5rem;
    background: white;
    height: 100%;
}

.mockup-prompt h4 {
    font-size: 1.125rem;
    color: #1f2937;
    margin-bottom: 1rem;
    font-family: 'Playfair Display', serif;
}

.mockup-text-area {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    min-height: 100px;
    display: flex;
    align-items: flex-start;
}

.typing-indicator {
    display: flex;
    gap: 0.25rem;
}

.typing-indicator span {
    width: 0.5rem;
    height: 0.5rem;
    background: #9ca3af;
    border-radius: 50%;
    animation: typing 1.4s ease-in-out infinite;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    margin-bottom: 3rem;
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.7);
    margin: 1.5rem 0;
    line-height: 1.6;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.link-group h4 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.125rem;
}

.link-group a {
    display: block;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    margin-bottom: 0.75rem;
    transition: color 0.3s ease;
}

.link-group a:hover {
    color: white;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.6);
}

.footer-copyright i {
    color: #ef4444;
}

.footer-badges {
    display: flex;
    gap: 1.5rem;
}

.badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

.badge i {
    color: var(--primary-color);
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .download-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .download-buttons {
        flex-direction: column;
    }

    .download-links {
        justify-content: center;
    }

    .footer-links {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-badges {
        justify-content: center;
    }

    .prompt-slide {
        padding: 2rem 1.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .mini-prompts {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Disable specific interactive elements - Windows Coming Soon Style */
.quiz-option,
.mobile-menu-btn,
input[type="submit"],
input[type="button"],
.memory-bubble,
.nav-actions a[href="#download"],
.nav-actions a[href="#get-started"],
.quiz-start,
.quiz-next,
.nav-actions .btn,
button[disabled],
.btn[disabled] {
    cursor: not-allowed !important;
    opacity: 0.4 !important;
    pointer-events: none !important;
    user-select: none !important;
    background: #f5f5f5 !important;
    color: #999999 !important;
    border-color: #cccccc !important;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1) !important;
    filter: grayscale(100%) !important;
}

/* Enable style cards with interactive styling */
.style-card {
    cursor: pointer !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    user-select: auto !important;
    filter: none !important;
    transition: all 0.3s ease !important;
    background: white !important;
    color: var(--text-primary) !important;
    border-color: var(--border) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.style-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

.style-card:active {
    transform: translateY(-2px) !important;
}

.style-card.active {
    border: 2px solid var(--primary-color) !important;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3) !important;
    transform: translateY(-5px) scale(1.02) !important;
}

/* Style notification styling */
.style-notification .notification-content h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

.style-notification .notification-content p {
    margin: 0 0 1.5rem 0;
    color: var(--text-secondary);
    line-height: 1.6;
}

.style-notification .features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.style-notification .feature-tag {
    background: var(--surface);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid var(--border);
}

/* Sparkle animation */
@keyframes sparkle {
    0% {
        opacity: 0;
        transform: scale(0) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1) rotate(180deg);
    }
    100% {
        opacity: 0;
        transform: scale(0) rotate(360deg);
    }
}

.sparkle {
    animation: sparkle 1s ease-out forwards;
    pointer-events: none;
}

/* Enable mini-reflections with interactive styling */
.mini-reflection {
    cursor: pointer !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    user-select: auto !important;
    filter: none !important;
    transition: all 0.3s ease !important;
    position: relative;
    overflow: hidden;
    background: var(--surface) !important;
    color: var(--text-primary) !important;
    border-color: var(--border) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.mini-reflection:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.mini-reflection:active {
    transform: translateY(-1px) scale(1.02) !important;
}

.mini-reflection.selected {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.4) !important;
}

/* Pulse animation for mini-reflections */
@keyframes pulse-glow {
    0% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
    }
    100% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
    }
}

.mini-reflection.pulsing {
    animation: pulse-glow 1.5s ease-in-out infinite;
}

/* Enable reflection navigation buttons */
.reflection-btn,
.nav-btn {
    cursor: pointer !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    user-select: auto !important;
    filter: none !important;
    background: var(--surface) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border) !important;
    transition: all 0.3s ease !important;
}

.reflection-btn:hover,
.nav-btn:hover {
    background: var(--primary-color) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.reflection-btn.active {
    background: var(--primary-color) !important;
    color: white !important;
}

/* Style download links properly */
.download-link {
    cursor: pointer !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    user-select: auto !important;
    filter: none !important;
    transition: all 0.3s ease !important;
}

.download-link:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Ensure footer links display properly */
.footer-links a {
    cursor: pointer !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    user-select: auto !important;
    filter: none !important;
    color: var(--text-secondary) !important;
    transition: all 0.3s ease !important;
}

.footer-links a:hover {
    color: var(--primary-color) !important;
}

/* Logo image styling - override original logo styles completely */
.logo .logo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 0.75rem;
    display: block;
}

/* Override original logo background when image is present */
.nav-brand .logo,
.footer-logo .logo {
    background: none !important;
    padding: 0 !important;
    overflow: hidden;
}

/* Reflection Modal Styling */
.reflection-modal .modal-content {
    background: white;
    border-radius: 1rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: all 0.3s ease;
}

.reflection-modal[style*="opacity: 1"] .modal-content {
    transform: scale(1);
}

.reflection-modal .modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.reflection-modal .modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

.reflection-modal .modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.reflection-modal .modal-close:hover {
    background: var(--surface);
    color: var(--text-primary);
}

.reflection-modal .modal-body {
    padding: 1.5rem;
}

.reflection-modal .reflection-question,
.reflection-modal .reflection-description {
    margin-bottom: 1.5rem;
}

.reflection-modal .modal-body h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.reflection-modal .modal-body p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.6;
}

.reflection-modal .reflection-question p {
    font-style: italic;
    font-size: 1.1rem;
    color: var(--text-primary);
    background: var(--surface);
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid var(--primary-color);
}

.reflection-modal .category-tag {
    display: inline-block;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: capitalize;
}

.reflection-modal .modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border);
    text-align: center;
}

.reflection-modal .modal-start {
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    border: none;
    color: white;
    font-weight: 500;
    cursor: not-allowed;
    opacity: 0.7;
    transition: all 0.3s ease;
}

/* Disable hover effects for disabled elements only */
.quiz-option:hover,
.memory-bubble:hover,
.nav-actions .btn:hover,
.nav-actions a[href="#download"]:hover,
.nav-actions a[href="#get-started"]:hover,
button[disabled]:hover,
.btn[disabled]:hover {
    transform: none !important;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1) !important;
    background: #f5f5f5 !important;
    border-color: #cccccc !important;
    color: #999999 !important;
}

/* Windows-style disabled appearance */
.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255,255,255,0.1) 2px,
        rgba(255,255,255,0.1) 4px
    );
    pointer-events: none;
}

/* Additional Windows disabled styling */
button,
.btn {
    position: relative !important;
    text-shadow: 1px 1px 0px rgba(255,255,255,0.5) !important;
    border: 1px solid #cccccc !important;
    background: linear-gradient(to bottom, #f8f8f8, #e8e8e8) !important;
}
